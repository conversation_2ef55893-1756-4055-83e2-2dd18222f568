const { EmbedBuilder, SlashCommandBuilder } = require('discord.js');
const EVEApi = require('../utils/eveApi');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('server')
        .setDescription('Check EVE Online server status'),
    async execute(interaction) {
        await interaction.deferReply();

        try {
            const data = await EVEApi.getServerStatus();

            const embed = new EmbedBuilder()
                .setTitle('EVE Online Server Status')
                .setColor(0x00ff00)
                .addFields(
                    { name: 'Players Online', value: data.players.toLocaleString(), inline: true },
                    { name: 'Server Version', value: data.server_version || 'Unknown', inline: true },
                    { name: 'Start Time', value: data.start_time || 'Unknown', inline: false }
                );

            await interaction.editReply({ embeds: [embed] });
        } catch (error) {
            await interaction.editReply(`Error checking server status: ${error.message}`);
        }
    }
};