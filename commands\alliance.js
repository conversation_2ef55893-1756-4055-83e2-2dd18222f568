const { EmbedBuilder } = require('discord.js');
const EVEApi = require('../utils/eveApi');

module.exports = {
    name: 'alliance',
    description: 'Alliance information and member corps',
    async execute(message, args) {
        const allianceName = args.join(' ');
        if (!allianceName) {
            return message.reply('Please provide an alliance name!');
        }

        try {
            const allianceId = await EVEApi.searchAlliance(allianceName);
            if (!allianceId) {
                return message.reply(`Alliance '${allianceName}' not found.`);
            }

            const allianceData = await EVEApi.getAlliance(allianceId);

            const embed = new EmbedBuilder()
                .setTitle(allianceData.name)
                .setColor(0x9900ff)
                .addFields(
                    { name: 'Ticker', value: `<${allianceData.ticker}>`, inline: true },
                    { name: 'Founded', value: new Date(allianceData.date_founded).toLocaleDateString(), inline: true },
                    { name: 'Executor Corp', value: allianceData.executor_corporation_name || 'Unknown', inline: true }
                )
                .setThumbnail(`https://images.evetech.net/alliances/${allianceId}/logo?size=128`);

            message.reply({ embeds: [embed] });
        } catch (error) {
            message.reply(`Error looking up alliance: ${error.message}`);
        }
    }
};