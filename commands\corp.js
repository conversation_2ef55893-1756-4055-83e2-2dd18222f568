const { EmbedBuilder } = require('discord.js');
const EVEApi = require('../utils/eveApi');

module.exports = {
    name: 'corp',
    description: 'Corporation info, member count, ticker',
    async execute(message, args) {
        const corpName = args.join(' ');
        if (!corpName) {
            return message.reply('Please provide a corporation name!');
        }

        try {
            const corpId = await EVEApi.searchCorporation(corpName);
            if (!corpId) {
                return message.reply(`Corporation '${corpName}' not found.`);
            }

            const corpData = await EVEApi.getCorporation(corpId);

            const embed = new EmbedBuilder()
                .setTitle(corpData.name)
                .setColor(0x0099ff)
                .addFields(
                    { name: 'Ticker', value: `[${corpData.ticker}]`, inline: true },
                    { name: 'Member Count', value: corpData.member_count.toLocaleString(), inline: true },
                    { name: 'CEO', value: corpData.ceo_name || 'Unknown', inline: true },
                    { name: 'Founded', value: new Date(corpData.date_founded).toLocaleDateString(), inline: true },
                    { name: 'Tax Rate', value: `${(corpData.tax_rate * 100).toFixed(1)}%`, inline: true }
                )
                .setThumbnail(`https://images.evetech.net/corporations/${corpId}/logo?size=128`);

            if (corpData.alliance_id) {
                const allianceData = await EVEApi.getAlliance(corpData.alliance_id);
                embed.addFields({ name: 'Alliance', value: allianceData.name, inline: true });
            }

            message.reply({ embeds: [embed] });
        } catch (error) {
            message.reply(`Error looking up corporation: ${error.message}`);
        }
    }
};