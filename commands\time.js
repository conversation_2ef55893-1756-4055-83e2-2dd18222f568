const { EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'time',
    description: 'EVE time and server events',
    async execute(message, args) {
        try {
            const now = new Date();
            const eveTime = now.toISOString().replace('T', ' ').substring(0, 19) + ' UTC';
            
            // Calculate daily downtime (11:00 UTC)
            const downtime = new Date(now);
            downtime.setUTCHours(11, 0, 0, 0);
            if (now > downtime) {
                downtime.setDate(downtime.getDate() + 1);
            }
            
            const timeToDowntime = Math.floor((downtime - now) / 1000 / 60 / 60);
            const minutesToDowntime = Math.floor(((downtime - now) / 1000 / 60) % 60);

            const embed = new EmbedBuilder()
                .setTitle('EVE Online Time')
                .setColor(0x0099ff)
                .addFields(
                    { name: 'Current EVE Time', value: eveTime, inline: false },
                    { name: 'Next Downtime', value: `${timeToDowntime}h ${minutesToDowntime}m`, inline: true },
                    { name: 'Downtime Schedule', value: '11:00 UTC Daily', inline: true }
                )
                .setTimestamp();

            message.reply({ embeds: [embed] });
        } catch (error) {
            message.reply(`Error getting time info: ${error.message}`);
        }
    }
};