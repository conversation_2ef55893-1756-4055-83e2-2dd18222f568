const { EmbedBuilder } = require('discord.js');
const EVEApi = require('../utils/eveApi');

module.exports = {
    name: 'history',
    description: 'Price history for items',
    async execute(message, args) {
        const itemName = args.join(' ');
        if (!itemName) {
            return message.reply('Please provide an item name!');
        }

        try {
            const typeId = await EVEApi.searchItem(itemName);
            if (!typeId) {
                return message.reply(`Item '${itemName}' not found.`);
            }

            const history = await EVEApi.getMarketHistory(10000002, typeId); // The Forge
            if (!history || history.length === 0) {
                return message.reply('No price history available.');
            }

            const recent = history.slice(-7); // Last 7 days
            const latest = recent[recent.length - 1];
            const weekAgo = recent[0];
            const change = ((latest.average - weekAgo.average) / weekAgo.average * 100).toFixed(2);

            const embed = new EmbedBuilder()
                .setTitle(`Price History - ${itemName}`)
                .setColor(change >= 0 ? 0x00ff00 : 0xff0000)
                .addFields(
                    { name: 'Current Average', value: `${latest.average.toLocaleString()} ISK`, inline: true },
                    { name: '7-Day Change', value: `${change}%`, inline: true },
                    { name: 'Volume (24h)', value: latest.volume.toLocaleString(), inline: true },
                    { name: 'Highest', value: `${latest.highest.toLocaleString()} ISK`, inline: true },
                    { name: 'Lowest', value: `${latest.lowest.toLocaleString()} ISK`, inline: true },
                    { name: 'Orders', value: latest.order_count.toLocaleString(), inline: true }
                )
                .setFooter({ text: 'Data from The Forge region' });

            message.reply({ embeds: [embed] });
        } catch (error) {
            message.reply(`Error fetching price history: ${error.message}`);
        }
    }
};