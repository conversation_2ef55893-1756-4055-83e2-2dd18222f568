const axios = require('axios');

class EVEApi {
    static async searchCharacter(name) {
        const response = await axios.get(`https://esi.evetech.net/latest/search/?categories=character&search=${encodeURIComponent(name)}`);
        return response.data.character && response.data.character[0] ? response.data.character[0] : null;
    }

    static async getCharacter(charId) {
        const response = await axios.get(`https://esi.evetech.net/latest/characters/${charId}/`);
        return response.data;
    }

    static async getCorporation(corpId) {
        const response = await axios.get(`https://esi.evetech.net/latest/corporations/${corpId}/`);
        return response.data;
    }

    static async getServerStatus() {
        const response = await axios.get('https://esi.evetech.net/latest/status/');
        return response.data;
    }

    static async searchItem(name) {
        const response = await axios.get(`https://esi.evetech.net/latest/search/?categories=inventory_type&search=${encodeURIComponent(name)}`);
        return response.data.inventory_type && response.data.inventory_type[0] ? response.data.inventory_type[0] : null;
    }

    static async getMarketOrders(regionId, typeId) {
        const response = await axios.get(`https://esi.evetech.net/latest/markets/${regionId}/orders/?type_id=${typeId}`);
        return response.data;
    }

    static async getKillmail(killmailId) {
        const response = await axios.get(`https://zkillboard.com/api/killID/${killmailId}/`);
        return response.data[0];
    }

    static async searchCorporation(name) {
        const response = await axios.get(`https://esi.evetech.net/latest/search/?categories=corporation&search=${encodeURIComponent(name)}`);
        return response.data.corporation && response.data.corporation[0] ? response.data.corporation[0] : null;
    }

    static async searchAlliance(name) {
        const response = await axios.get(`https://esi.evetech.net/latest/search/?categories=alliance&search=${encodeURIComponent(name)}`);
        return response.data.alliance && response.data.alliance[0] ? response.data.alliance[0] : null;
    }

    static async getAlliance(allianceId) {
        const response = await axios.get(`https://esi.evetech.net/latest/alliances/${allianceId}/`);
        return response.data;
    }

    static async getMarketHistory(regionId, typeId) {
        const response = await axios.get(`https://esi.evetech.net/latest/markets/${regionId}/history/?type_id=${typeId}`);
        return response.data;
    }

    static async searchSystem(name) {
        const response = await axios.get(`https://esi.evetech.net/latest/search/?categories=solar_system&search=${encodeURIComponent(name)}`);
        return response.data.solar_system && response.data.solar_system[0] ? response.data.solar_system[0] : null;
    }

    static async getSystem(systemId) {
        const response = await axios.get(`https://esi.evetech.net/latest/universe/systems/${systemId}/`);
        return response.data;
    }

    static async getRoute(origin, destination) {
        const response = await axios.get(`https://esi.evetech.net/latest/route/${origin}/${destination}/`);
        return response.data;
    }

    static async getRegions() {
        const response = await axios.get('https://esi.evetech.net/latest/universe/regions/');
        return response.data;
    }

    static async getRegion(regionId) {
        const response = await axios.get(`https://esi.evetech.net/latest/universe/regions/${regionId}/`);
        return response.data;
    }
}

module.exports = EVEApi;