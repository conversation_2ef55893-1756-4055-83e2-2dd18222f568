const { EmbedBuilder } = require('discord.js');
const EVEApi = require('../utils/eveApi');

module.exports = {
    name: 'price',
    description: 'Check item price in Jita',
    async execute(message, args) {
        const itemName = args.join(' ');
        if (!itemName) {
            return message.reply('Please provide an item name!');
        }

        try {
            const typeId = await EVEApi.searchItem(itemName);
            if (!typeId) {
                return message.reply(`Item '${itemName}' not found.`);
            }

            const orders = await EVEApi.getMarketOrders(10000002, typeId);
            
            const buyOrders = orders.filter(o => o.is_buy_order);
            const sellOrders = orders.filter(o => !o.is_buy_order);

            const highestBuy = buyOrders.length > 0 ? Math.max(...buyOrders.map(o => o.price)) : 0;
            const lowestSell = sellOrders.length > 0 ? Math.min(...sellOrders.map(o => o.price)) : 0;

            const embed = new EmbedBuilder()
                .setTitle(`Market Data - ${itemName}`)
                .setColor(0x00ff00)
                .addFields(
                    { name: 'Highest Buy', value: `${highestBuy.toLocaleString()} ISK`, inline: true },
                    { name: 'Lowest Sell', value: `${lowestSell.toLocaleString()} ISK`, inline: true },
                    { name: 'Region', value: 'The Forge (Jita)', inline: false }
                );

            message.reply({ embeds: [embed] });
        } catch (error) {
            message.reply(`Error checking price: ${error.message}`);
        }
    }
};