const fs = require('fs');
const path = require('path');
const { REST, Routes } = require('discord.js');

function loadSlashCommands(client) {
    const slashCommandsPath = path.join(__dirname, '..', 'slashCommands');
    const slashCommandFiles = fs.readdirSync(slashCommandsPath).filter(file => file.endsWith('.js'));

    const commands = [];
    
    for (const file of slashCommandFiles) {
        const command = require(path.join(slashCommandsPath, file));
        client.slashCommands.set(command.data.name, command);
        commands.push(command.data.toJSON());
    }

    client.on('interactionCreate', async (interaction) => {
        if (!interaction.isChatInputCommand()) return;

        const command = client.slashCommands.get(interaction.commandName);
        if (!command) return;

        try {
            await command.execute(interaction);
        } catch (error) {
            console.error(error);
            await interaction.reply({ content: 'There was an error executing this command!', ephemeral: true });
        }
    });

    console.log(`Loaded ${slashCommandFiles.length} slash commands`);
    return commands;
}

async function deploySlashCommands(client, commands, token, clientId) {
    const rest = new REST().setToken(token);
    
    try {
        console.log('Registering slash commands...');
        await rest.put(
            Routes.applicationCommands(clientId),
            { body: commands }
        );
        console.log('Successfully registered slash commands.');
    } catch (error) {
        console.error('Error registering slash commands:', error);
    }
}

module.exports = { loadSlashCommands, deploySlashCommands };