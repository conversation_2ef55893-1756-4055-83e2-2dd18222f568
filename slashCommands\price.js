const { EmbedBuilder, SlashCommandBuilder } = require('discord.js');
const EVEApi = require('../utils/eveApi');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('price')
        .setDescription('Check item price in Jita')
        .addStringOption(option =>
            option.setName('item')
                .setDescription('Item name to check price for')
                .setRequired(true)),
    async execute(interaction) {
        const itemName = interaction.options.getString('item');
        
        await interaction.deferReply();

        try {
            const typeId = await EVEApi.searchItem(itemName);
            if (!typeId) {
                return interaction.editReply(`Item '${itemName}' not found.`);
            }

            const orders = await EVEApi.getMarketOrders(10000002, typeId);
            
            const buyOrders = orders.filter(o => o.is_buy_order);
            const sellOrders = orders.filter(o => !o.is_buy_order);

            const highestBuy = buyOrders.length > 0 ? Math.max(...buyOrders.map(o => o.price)) : 0;
            const lowestSell = sellOrders.length > 0 ? Math.min(...sellOrders.map(o => o.price)) : 0;

            const embed = new EmbedBuilder()
                .setTitle(`Market Data - ${itemName}`)
                .setColor(0x00ff00)
                .addFields(
                    { name: 'Highest Buy', value: `${highestBuy.toLocaleString()} ISK`, inline: true },
                    { name: 'Lowest Sell', value: `${lowestSell.toLocaleString()} ISK`, inline: true },
                    { name: 'Region', value: 'The Forge (Jita)', inline: false }
                );

            await interaction.editReply({ embeds: [embed] });
        } catch (error) {
            await interaction.editReply(`Error checking price: ${error.message}`);
        }
    }
};