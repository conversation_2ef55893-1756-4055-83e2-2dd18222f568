const { EmbedBuilder } = require('discord.js');
const EVEApi = require('../utils/eveApi');

module.exports = {
    name: 'profit',
    description: 'Buy/sell profit margins',
    async execute(message, args) {
        const itemName = args.join(' ');
        if (!itemName) {
            return message.reply('Please provide an item name!');
        }

        try {
            const typeId = await EVEApi.searchItem(itemName);
            if (!typeId) {
                return message.reply(`Item '${itemName}' not found.`);
            }

            const orders = await EVEApi.getMarketOrders(10000002, typeId); // The Forge
            
            const buyOrders = orders.filter(o => o.is_buy_order);
            const sellOrders = orders.filter(o => !o.is_buy_order);

            if (buyOrders.length === 0 || sellOrders.length === 0) {
                return message.reply('Insufficient market data for profit calculation.');
            }

            const highestBuy = Math.max(...buyOrders.map(o => o.price));
            const lowestSell = Math.min(...sellOrders.map(o => o.price));
            
            const profit = lowestSell - highestBuy;
            const margin = ((profit / lowestSell) * 100).toFixed(2);

            const embed = new EmbedBuilder()
                .setTitle(`Profit Analysis - ${itemName}`)
                .setColor(profit > 0 ? 0x00ff00 : 0xff0000)
                .addFields(
                    { name: 'Buy Price', value: `${highestBuy.toLocaleString()} ISK`, inline: true },
                    { name: 'Sell Price', value: `${lowestSell.toLocaleString()} ISK`, inline: true },
                    { name: 'Profit', value: `${profit.toLocaleString()} ISK`, inline: true },
                    { name: 'Margin', value: `${margin}%`, inline: true },
                    { name: 'Status', value: profit > 0 ? '✅ Profitable' : '❌ Not Profitable', inline: true }
                )
                .setFooter({ text: 'Jita market data - excludes taxes and fees' });

            message.reply({ embeds: [embed] });
        } catch (error) {
            message.reply(`Error calculating profit: ${error.message}`);
        }
    }
};