const { EmbedBuilder } = require('discord.js');
const EVEApi = require('../utils/eveApi');

module.exports = {
    name: 'route',
    description: 'Jump route calculator',
    async execute(message, args) {
        if (args.length < 2) {
            return message.reply('Please provide origin and destination systems! Example: `!route Jita Amarr`');
        }

        const originName = args[0];
        const destinationName = args.slice(1).join(' ');

        try {
            const originId = await EVEApi.searchSystem(originName);
            const destinationId = await EVEApi.searchSystem(destinationName);

            if (!originId) {
                return message.reply(`Origin system '${originName}' not found.`);
            }
            if (!destinationId) {
                return message.reply(`Destination system '${destinationName}' not found.`);
            }

            const route = await EVEApi.getRoute(originId, destinationId);
            
            if (!route || route.length === 0) {
                return message.reply('No route found between these systems.');
            }

            const jumps = route.length - 1;
            const routePreview = route.length > 10 ? 
                `${route.slice(0, 5).join(' → ')} ... → ${route.slice(-5).join(' → ')}` :
                route.join(' → ');

            const embed = new EmbedBuilder()
                .setTitle(`Route: ${originName} → ${destinationName}`)
                .setColor(0x0099ff)
                .addFields(
                    { name: 'Total Jumps', value: jumps.toString(), inline: true },
                    { name: 'Systems', value: route.length.toString(), inline: true },
                    { name: 'Route Preview', value: routePreview.length > 1024 ? 'Route too long to display' : routePreview, inline: false }
                );

            message.reply({ embeds: [embed] });
        } catch (error) {
            message.reply(`Error calculating route: ${error.message}`);
        }
    }
};