const { EmbedBuilder } = require('discord.js');
const EVEApi = require('../utils/eveApi');

module.exports = {
    name: 'character',
    description: 'Look up EVE character information',
    async execute(message, args) {
        const characterName = args.join(' ');
        if (!characterName) {
            return message.reply('Please provide a character name!');
        }

        try {
            const charId = await EVEApi.searchCharacter(characterName);
            if (!charId) {
                return message.reply(`Character '${characterName}' not found.`);
            }

            const charData = await EVEApi.getCharacter(charId);
            const corpData = await EVEApi.getCorporation(charData.corporation_id);

            const embed = new EmbedBuilder()
                .setTitle(charData.name)
                .setColor(0x00ff00)
                .addFields(
                    { name: 'Corporation', value: corpData.name, inline: true },
                    { name: 'Security Status', value: (charData.security_status || 0).toFixed(2), inline: true }
                )
                .setThumbnail(`https://images.evetech.net/characters/${charId}/portrait?size=128`);

            message.reply({ embeds: [embed] });
        } catch (error) {
            message.reply(`Error looking up character: ${error.message}`);
        }
    }
};