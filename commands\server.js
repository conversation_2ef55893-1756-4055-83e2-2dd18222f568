const { EmbedBuilder } = require('discord.js');
const EVEApi = require('../utils/eveApi');

module.exports = {
    name: 'server',
    description: 'Check EVE Online server status',
    async execute(message, args) {
        try {
            const data = await EVEApi.getServerStatus();

            const embed = new EmbedBuilder()
                .setTitle('EVE Online Server Status')
                .setColor(0x00ff00)
                .addFields(
                    { name: 'Players Online', value: data.players.toLocaleString(), inline: true },
                    { name: 'Server Version', value: data.server_version || 'Unknown', inline: true },
                    { name: 'Start Time', value: data.start_time || 'Unknown', inline: false }
                );

            message.reply({ embeds: [embed] });
        } catch (error) {
            message.reply(`Error checking server status: ${error.message}`);
        }
    }
};