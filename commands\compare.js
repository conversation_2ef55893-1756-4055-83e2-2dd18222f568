const { EmbedBuilder } = require('discord.js');
const EVEApi = require('../utils/eveApi');

const MAJOR_REGIONS = {
    10000002: 'The Forge (Jita)',
    10000043: 'Domain (Amarr)',
    10000032: '<PERSON><PERSON> (Dodixie)',
    10000030: '<PERSON>ima<PERSON> (<PERSON><PERSON>)'
};

module.exports = {
    name: 'compare',
    description: 'Compare prices across regions',
    async execute(message, args) {
        const itemName = args.join(' ');
        if (!itemName) {
            return message.reply('Please provide an item name!');
        }

        try {
            const typeId = await EVEApi.searchItem(itemName);
            if (!typeId) {
                return message.reply(`Item '${itemName}' not found.`);
            }

            const embed = new EmbedBuilder()
                .setTitle(`Price Comparison - ${itemName}`)
                .setColor(0x00ff99);

            for (const [regionId, regionName] of Object.entries(MAJOR_REGIONS)) {
                try {
                    const orders = await EVEApi.getMarketOrders(regionId, typeId);
                    const sellOrders = orders.filter(o => !o.is_buy_order);
                    const lowestSell = sellOrders.length > 0 ? Math.min(...sellOrders.map(o => o.price)) : 0;
                    
                    embed.addFields({
                        name: regionName,
                        value: lowestSell > 0 ? `${lowestSell.toLocaleString()} ISK` : 'No orders',
                        inline: true
                    });
                } catch (err) {
                    embed.addFields({
                        name: regionName,
                        value: 'Error',
                        inline: true
                    });
                }
            }

            message.reply({ embeds: [embed] });
        } catch (error) {
            message.reply(`Error comparing prices: ${error.message}`);
        }
    }
};