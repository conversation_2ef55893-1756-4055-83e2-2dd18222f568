const { Client, GatewayIntentBits, Collection } = require('discord.js');
const { loadCommands } = require('./handlers/commandHandler');
const { loadEvents } = require('./handlers/eventHandler');
const { loadSlashCommands, deploySlashCommands } = require('./handlers/slashCommandHandler');

const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
    ]
});

client.commands = new Collection();
client.slashCommands = new Collection();

loadCommands(client);
loadEvents(client);
const slashCommands = loadSlashCommands(client);

client.once('ready', async () => {
    await deploySlashCommands(client, slashCommands, 'YOUR_BOT_TOKEN_HERE', 'YOUR_CLIENT_ID_HERE');
});

// Replace with your bot token
client.login('YOUR_BOT_TOKEN_HERE');