require('dotenv').config();
const { Client, GatewayIntentBits, Collection } = require('discord.js');
const { loadCommands } = require('./handlers/commandHandler');
const { loadEvents } = require('./handlers/eventHandler');
const { loadSlashCommands, deploySlashCommands } = require('./handlers/slashCommandHandler');

const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
    ]
});

client.commands = new Collection();
client.slashCommands = new Collection();

loadCommands(client);
loadEvents(client);
const slashCommands = loadSlashCommands(client);

client.once('ready', async () => {
    await deploySlashCommands(client, slashCommands, process.env.DISCORD_BOT_TOKEN, process.env.DISCORD_CLIENT_ID);
});

// Login with bot token from environment variables
client.login(process.env.DISCORD_BOT_TOKEN);