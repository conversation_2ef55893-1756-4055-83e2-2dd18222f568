const { EmbedBuilder } = require('discord.js');
const EVEApi = require('../utils/eveApi');

module.exports = {
    name: 'system',
    description: 'Solar system info, security, stations',
    async execute(message, args) {
        const systemName = args.join(' ');
        if (!systemName) {
            return message.reply('Please provide a system name!');
        }

        try {
            const systemId = await EVEApi.searchSystem(systemName);
            if (!systemId) {
                return message.reply(`System '${systemName}' not found.`);
            }

            const systemData = await EVEApi.getSystem(systemId);
            const security = systemData.security_status.toFixed(1);
            const securityClass = security >= 0.5 ? 'High Sec' : security > 0.0 ? 'Low Sec' : 'Null Sec';

            const embed = new EmbedBuilder()
                .setTitle(systemData.name)
                .setColor(security >= 0.5 ? 0x00ff00 : security > 0.0 ? 0xffff00 : 0xff0000)
                .addFields(
                    { name: 'Security Status', value: `${security} (${securityClass})`, inline: true },
                    { name: 'Constellation', value: systemData.constellation_name || 'Unknown', inline: true },
                    { name: 'Region', value: systemData.region_name || 'Unknown', inline: true },
                    { name: 'Stations', value: systemData.stations?.length?.toString() || '0', inline: true },
                    { name: 'Planets', value: systemData.planets?.length?.toString() || '0', inline: true },
                    { name: 'Star Class', value: systemData.star?.type_name || 'Unknown', inline: true }
                );

            message.reply({ embeds: [embed] });
        } catch (error) {
            message.reply(`Error looking up system: ${error.message}`);
        }
    }
};