const { EmbedBuilder } = require('discord.js');
const EVEApi = require('../utils/eveApi');

module.exports = {
    name: 'killmail',
    description: 'Show killmail details from zKillboard',
    async execute(message, args) {
        const killmailId = args[0];
        if (!killmailId) {
            return message.reply('Please provide a killmail ID!');
        }

        try {
            const killmail = await EVEApi.getKillmail(killmailId);
            if (!killmail) {
                return message.reply(`Killmail ${killmailId} not found.`);
            }

            const victim = killmail.victim;
            const attackers = killmail.attackers.length;
            const finalBlow = killmail.attackers.find(a => a.final_blow);

            const embed = new EmbedBuilder()
                .setTitle(`Killmail ${killmailId}`)
                .setColor(0xff0000)
                .setURL(`https://zkillboard.com/kill/${killmailId}/`)
                .addFields(
                    { name: '<PERSON><PERSON><PERSON>', value: victim.character_name || 'Unknown', inline: true },
                    { name: 'Ship', value: victim.ship_type_name || 'Unknown', inline: true },
                    { name: 'Attackers', value: attackers.toString(), inline: true },
                    { name: 'Final Blow', value: finalBlow?.character_name || 'Unknown', inline: true },
                    { name: 'Total Value', value: `${killmail.zkb?.totalValue?.toLocaleString() || 0} ISK`, inline: true },
                    { name: 'System', value: killmail.solar_system_name || 'Unknown', inline: true }
                )
                .setTimestamp(new Date(killmail.killmail_time));

            message.reply({ embeds: [embed] });
        } catch (error) {
            message.reply(`Error fetching killmail: ${error.message}`);
        }
    }
};