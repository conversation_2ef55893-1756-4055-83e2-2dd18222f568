const { EmbedBuilder, SlashCommandBuilder } = require('discord.js');
const EVEApi = require('../utils/eveApi');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('character')
        .setDescription('Look up EVE character information')
        .addStringOption(option =>
            option.setName('name')
                .setDescription('Character name to look up')
                .setRequired(true)),
    async execute(interaction) {
        const characterName = interaction.options.getString('name');
        
        await interaction.deferReply();

        try {
            const charId = await EVEApi.searchCharacter(characterName);
            if (!charId) {
                return interaction.editReply(`Character '${characterName}' not found.`);
            }

            const charData = await EVEApi.getCharacter(charId);
            const corpData = await EVEApi.getCorporation(charData.corporation_id);

            const embed = new EmbedBuilder()
                .setTitle(charData.name)
                .setColor(0x00ff00)
                .addFields(
                    { name: 'Corporation', value: corpData.name, inline: true },
                    { name: 'Security Status', value: (charData.security_status || 0).toFixed(2), inline: true }
                )
                .setThumbnail(`https://images.evetech.net/characters/${charId}/portrait?size=128`);

            await interaction.editReply({ embeds: [embed] });
        } catch (error) {
            await interaction.editReply(`Error looking up character: ${error.message}`);
        }
    }
};