# EVE Online Discord Bot

A Discord bot for EVE Online with character lookup, server status, and market data.

## Setup

1. Install dependencies:
```bash
npm install
```

2. Create a Discord application and bot at https://discord.com/developers/applications

3. Replace `YOUR_BOT_TOKEN_HERE` in bot.js with your actual bot token

4. Run the bot:
```bash
npm start
```

## Commands

- `!character <name>` - Look up character information
- `!server` - Check EVE Online server status  
- `!price <item>` - Check item price in Jita

## Bot Permissions

Make sure your bot has these permissions:
- Send Messages
- Use Slash Commands
- Embed Links
- Read Message History